<template>
  <div class="index nearBy appnearBy">
    <!-- 企业微信弹框 -->
    <van-popup
      v-model="popup.show"
      round
    >
      <div class="wechpopContent">
        <!-- <div class="remove" @click="handlePopupFalse">
          <l-icon name="close" color="#333333" size="30"/>
        </div> -->
        <!-- <div class="pongetitle">有问题找店长</div> -->
        <div class="bgimg">
          <img mode="widthFix" src="https://img1.shop.10086.cn/fs/goods/fs_62d03471e4b0355791a71fa5.png" alt="">
        </div>
        <div class="text">
          <div class="dzimg">
            <img :src="popup.wxCode" alt="">
            <img :src="popup.avatar" class="positiondzimg" alt="">
          </div>
          <div class="left">
            <div class="tit">
              添加方式:
            </div>
            <div class="com">
              <p>1、请使用微信扫码添加店铺客服</p>
              <p>2、截图保存图片,使用微信扫一扫添加店长好友</p>
            </div>
          </div>

        </div>
      </div>
    </van-popup>

    <div :class="['nav',data.isSearchBoolean?'searchActive':'']">
      <switch-location :color="'#fff'" :user-location="data.userLocation" @confirm="switchConfirm" />
      <van-search
        v-model="data.keywords"
        placeholder="请输入查询的店铺或相关标签"
        :disabled="!data.userLocation || (data.userLocation.latitude&&!data.userChooseLocation.provinceId) || !data.userLocation.latitude"
        @search="getNearShopNav"
        @clear="getNearShopNav"
      />
      <template v-if="data.navShopList && data.shopListNav && data.shopListNav.length">
        <div class="nav-search-tkzh" @click="data.navShopList = false"></div>
        <ul class="nav-search-con">
          <li
            v-for="(itemn, keyn) in data.shopListNav"
            :key="keyn"
            @click="
              goToShop(itemn.shopId)
              data.navShopList = false
            "
          >
            {{ itemn.shopShortName }}
          </li>
        </ul>
      </template>
    </div>
    <div class="shop-message-top">
      <!-- 推荐门店 -->
      <div v-if="(data.shopInfo && Object.keys(data.shopInfo).length) || data.goodsItemShop" class="shop-message nearbyitem">
        <img
          v-if="data.shopInfo"
          class="message-sel"
          src="@/assets/nearby/appnearby/shop_sel.png"
          alt=""
        />
        <div v-if="data.shopInfo" :class="['shop-message-tjmd', data.shopInfo.expansion ? 'heightYihang' : '']">
          <div class="center">
            <aside class="con">
              <div class="itemLeft">
                <div class="title">
                  <div>
                    {{ data.shopInfo.shopShortName }}
                  </div>
                  <dt>
                    <a :href="'tel:' + data.shopInfo.kefuPhone" class="telNumber">
                      <img
                        src="~@/assets/index_normal/jajsdj.png"
                        alt=""
                      /></a>
                  </dt>
                  <dt>
                    <a v-if="data.shopInfo.qrCode" href="javascript:;" class="telNumber" @click="handleQrcode(data.shopInfo)">
                      <img
                        src="https://img1.shop.10086.cn/fs/goods/fs_635642fae4b0fdd36b797c57.png"
                        alt=""
                      /></a>
                  </dt>
                </div>


                <div v-if="data.shopInfo.shopOpenStatus != 1" class="businesscontent">
                  <!-- 5营业中 6休息中 1不显示 2关厅 -->
                  <img
                    v-if="data.shopInfo.shopOpenStatus === 5"
                    src="~@/assets/index_img/shop_open.png"
                    alt=""
                  />
                  <img
                    v-if="data.shopInfo.shopOpenStatus === 6"
                    src="~@/assets/index_img/shop_close.png"
                    alt=""
                  />
                  <img
                    v-if="data.shopInfo.shopOpenStatus === 2"
                    src="~@/assets/index_img/shop_close_grey.png"
                    alt=""
                  />
                  <span>
                    {{
                      data.shopInfo.shopOpenStatus === 5
                        ? "营业中"
                        : data.shopInfo.shopOpenStatus === 6
                          ? "休息中"
                          : data.shopInfo.shopOpenStatus === 2
                            ? "关厅"
                            : ""
                    }}
                  </span>
                  <span v-if="data.shopInfo.shopOpenStatus === 6 && getCurrentHour(data.shopInfo.businessHours)!=='店铺休息'">营业时间</span>
                  <span
                    v-if="
                      data.shopInfo.shopOpenStatus === 5 ||
                        data.shopInfo.shopOpenStatus === 6
                    "
                  >
                    {{
                      getCurrentHour(
                        data.shopInfo.businessHours,
                        data.shopInfo.shopOpenStatus
                      )
                    }}
                  </span>
                </div>

                <div class="shop-address">
                  <div class="tesleft">
                    <p>
                      <img
                        src="@/assets/nearby/appnearby/shop_vip.png"
                        alt=""
                      /><span class="">{{ data.shopInfo.shopPropaganda }}</span>
                    </p>
                  </div>
                </div>

                <p class="address">
                  <img
                    src="@/assets/nearby/locationicon.png"
                    class="icon icon_adress"
                  />
                  <span v-if="data.shopInfo.distance" class="distance">
                    {{
                      data.shopInfo.distance > 1000
                        ? (data.shopInfo.distance / 1000).toFixed(2) + "km"
                        : data.shopInfo.distance.toFixed() + "m"
                    }}
                    {{
                      data.shopInfo.address &&
                        data.shopInfo.address.replace(/[\n|\s]+/g, "")
                        ? "|"
                        : ""
                    }}
                  </span>

                  <span :class="['addressName','paddingRigth']" :style="{'white-space':data.shopInfo.isShowExpansionButton?'nowrap':'inherit'}">{{ data.shopInfo.address }}</span>
                </p>
              </div>
              <div class="itemRight">
                <img
                  v-if="data.shopInfo.nearbyShopPicture"
                  :src="myGetImg(data.shopInfo.nearbyShopPicture)"
                />
                <img v-else src="~@/assets/nearby/default_img.png" alt="" />
              </div>
            </aside>
          </div>

          <!-- 标签列表 -->
          <div v-if="data.shopInfo.tagList && data.shopInfo.tagList.length" :class="['tagList','paddingleft0', !data.shopInfo.expansion ? 'heightYihang' : '']">
            <dl class="dl">
              <div class="one">
                <dt v-for="(tag,index) in data.shopInfo.tagList.slice(0,3)" :key="index">
                  {{ tag }}
                </dt>
              </div>
              <div class="other">
                <dt v-for="(tag,index) in data.shopInfo.tagList.slice(3)" :key="index">
                  {{ tag }}
                </dt>
              </div>
            </dl>

          </div>

          <!-- 展开收缩 -->
          <div v-if="data.shopInfo.isShowExpansionButton" class="zhankai" style="padding:0;">
            <span v-if="!data.shopInfo.expansion" @click="data.shopInfo.expansion = true">
              <van-icon name="arrow-down" size="15" color="#333333" />
              <i>展开</i>
            </span>
            <span v-if="data.shopInfo.expansion" @click="data.shopInfo.expansion = false">
              <van-icon name="arrow-up" size="15" color="#333333" />
              <i>收起</i>
            </span>
          </div>

          <div class="change">
            <a
              href="javascript:void(0);"
              class="tesleft"
              @click="goToShop(data.shopInfo.shopId)"
            >
              进店逛逛</a>
            <a
              v-if="data.shopInfo.show == 1"
              href="javascript:void(0);"
              class="tesright"
              @click="quhao(data.shopInfo.shopId)"
            >
              立即取号</a>
          </div>
        </div>
        <div v-if="data.shopInfo && data.goodsItemShop" class="line"></div>
        <div v-if="data.goodsItemShop" class="shop-message-bdrx">
          <h3>本店热销</h3>
          <div class="hot-product-card">
            <div
              v-for="items in data.goodsItemShop"
              :key="items.goodsId"
              class="goods-item"
            >
              <a :href="filterGoodsLink(items.goodsLink)">
                <img
                  v-if="items.imgUrl !== ''"
                  :src="items.imgUrl"
                  class="image image1"
                />
                <img v-else :src="items.picture" class="image image1" />
                <div>
                  <p class="title1 over-ell">
                    {{ items.goodsName }}
                  </p>
                  <p class="title2 over-ell">
                    {{ items.title }}
                  </p>
                  <PriceCom
                    v-if="items.priceFrom"
                    :price-from="items.priceFrom"
                    :shop-price="items.shopPrice"
                    :price-section="items.priceSection"
                    price-class="numColor"
                  />
                  <p v-else class="num-color">
                    <small>￥</small><b>{{ (items.price / 100).toFixed(2) }}</b>
                  </p>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
      <!-- 精品店 -->
      <div v-if="data.boutiquelist && data.boutiquelist.length" class="boutique">
        <swiper :options="data.option">
          <swiper-slide v-for="item in data.boutiquelist" :key="item.shopId">
            <div class="boutique-list nearbyitem">
              <div class="boutique-title">
                <img
                  src="@/assets/nearby/appnearby/boutique-title.png"
                  alt=""
                />
              </div>
              <div v-if="data.shopInfo" :class="['shop-message-tjmd',item.expansion ? 'heightYihang' : '']">
                <div class="center">
                  <aside class="con">
                    <div class="itemLeft">
                      <!-- <p class="title">
                      {{ item.shopShortName }}
                    </p> -->
                      <div class="title">
                        <div>
                          {{ item.shopShortName }}
                        </div>
                        <dt>
                          <a :href="'tel:' + item.kefuPhone" class="telNumber">
                            <img
                              src="~@/assets/index_normal/jajsdj.png"
                              alt=""
                            /></a>
                        </dt>
                        <dt>
                          <a v-if="item.qrCode" href="javascript:;" class="telNumber" @click="handleQrcode(item)">
                            <img
                              src="https://img1.shop.10086.cn/fs/goods/fs_635642fae4b0fdd36b797c57.png"
                              alt=""
                            /></a>
                        </dt>
                      </div>
                      <div v-if="item.shopOpenStatus != 1" class="businesscontent">
                        <!-- 5营业中 6休息中 1不显示 2关厅 -->
                        <img
                          v-if="item.shopOpenStatus === 5"
                          src="~@/assets/index_img/shop_open.png"
                          alt=""
                        />
                        <img
                          v-if="item.shopOpenStatus === 6"
                          src="~@/assets/index_img/shop_close.png"
                          alt=""
                        />
                        <img
                          v-if="item.shopOpenStatus === 2"
                          src="~@/assets/index_img/shop_close_grey.png"
                          alt=""
                        />
                        <span>
                          {{
                            item.shopOpenStatus === 5
                              ? "营业中"
                              : item.shopOpenStatus === 6
                                ? "休息中"
                                : item.shopOpenStatus === 2
                                  ? "关厅"
                                  : ""
                          }}
                        </span>
                        <span v-if="item.shopOpenStatus === 6 && getCurrentHour(item.businessHours)!=='店铺休息'">营业时间</span>
                        <span
                          v-if="
                            item.shopOpenStatus === 5 || item.shopOpenStatus === 6
                          "
                        >
                          {{
                            getCurrentHour(
                              item.businessHours,
                              item.shopOpenStatus
                            )
                          }}
                        </span>
                      </div>
                      <div class="shop-address">
                        <div class="tesleft">
                          <p>
                            <img
                              src="@/assets/nearby/appnearby/shop_vip.png"
                              alt=""
                            /><span>{{ item.shopPropaganda }}</span>
                          </p>
                        </div>
                      </div>
                      <p class="address">
                        <img
                          src="@/assets/nearby/locationicon.png"
                          class="icon icon_adress"
                        />
                        <span v-if="item.distance" class="distance">
                          {{
                            item.distance > 1000
                              ? (item.distance / 1000).toFixed(2) + "km"
                              : item.distance.toFixed() + "m"
                          }}
                          {{
                            item.address && item.address.replace(/[\n|\s]+/g, "")
                              ? "|"
                              : ""
                          }}
                        </span>
                        <span :class="['addressName','paddingRigth']" :style="{'white-space':item.isShowExpansionButton?'nowrap':'inherit'}">{{ item.address }}</span>
                      </p>

                    </div>
                    <div class="itemRight">
                      <img
                        v-if="item.nearbyShopPicture"
                        :src="myGetImg(item.nearbyShopPicture)"
                      />
                      <img v-else src="~@/assets/nearby/default_img.png" alt="" />
                    </div>
                  </aside>
                </div>

                <!-- 标签列表 -->
                <div v-if="item.tagList && item.tagList.length" :class="['tagList', !item.expansion ? 'heightYihang' : '']">
                  <dl class="dl">
                    <div class="one">
                      <dt v-for="(tag,index) in item.tagList.slice(0,3)" :key="index">
                        {{ tag }}
                      </dt>
                    </div>
                    <div class="other">
                      <dt v-for="(tag,index) in item.tagList.slice(3)" :key="index">
                        {{ tag }}
                      </dt>
                    </div>
                  </dl>

                </div>

                <!-- 展开收缩 -->
                <div v-if="item.isShowExpansionButton" class="zhankai">
                  <span v-if="!item.expansion" @click="item.expansion = true">
                    <van-icon name="arrow-down" size="15" color="#333333" />
                    <i>展开</i>
                  </span>
                  <span v-if="item.expansion" @click="item.expansion = false">
                    <van-icon name="arrow-up" size="15" color="#333333" />
                    <i>收起</i>
                  </span>
                </div>

                <div class="change-buten">
                  <a
                    href="javascript:void(0);"
                    class="tesleft"
                    @click="goToShop(item.shopId)"
                  >
                    <img
                      src="@/assets/nearby/appnearby/shop_ice.png"
                      alt=""
                    /><span>进店逛逛</span></a>
                  <van-icon v-if="item.show == 1" name="minus" />
                  <!-- <a :href="'tel:' + item.kefuPhone" class="tesright">
                    <img
                      src="@/assets/nearby/appnearby/ice_sj.png"
                      alt=""
                    /><span>电话联系</span></a> -->
                  <a
                    v-if="item.show == 1"
                    href="javascript:void(0);"
                    class="tesright"
                    @click="quhao(item.shopId)"
                  >
                    <img
                      src="https://img1.shop.10086.cn/fs/goods/fs_635f38c4e4b0fdd3b5a921a5.png"
                      alt=""
                    /><span>立即取号</span></a>
                </div>
              </div>
            </div>
          </swiper-slide>
          <div slot="pagination" class="swiper-pagination"></div>
        </swiper>
      </div>
      <div v-if="!data.loading && data.shopList" class="cm-icon">
        <ul class="item">
          <li
            v-for="(tit, index) in data.tabTitle"
            :key="index"
            :class="{ active: data.cur === index }"
            @click="switchTab(index)"
          >
            {{ tit }}
            <img
              v-if="data.cur === index"
              src="~@/assets/nearby/appnearby/ice_nav.png"
            />
          </li>
        </ul>
      </div>
      <div v-if="data.loading && data.pageNum==1" class="loading-area">
        <van-loading />
        <p>附近店铺搜索中，请稍候</p>
      </div>
      <van-list
        v-if="!data.loading && data.shopList && data.shopList.length >= 1"
        v-model="data.loadingList"
        :finished="data.finished"
        :finished-text="
          data.showNoYyData
            ? '已经到底了没找到您想要的店铺吗？搜索店铺名称试试吧。'
            : '上拉查看更多'
        "
        class="vant-clearfix"
        @load="data.cur === 0 ? getNearShop() : getVipShop()"
      >
        <ul class="shop-list">
          <!--:class="item.shopId==shopInfo.shopId?'noimg':''"*/-->
          <li
            v-for="(item, key) in data.shopList"
            :key="key"
            :class="[item.shopId === data.shopInfo.shopId ? 'noimg' : '', item.expansion ? 'heightYihang' : '']"
            class="nearbyitem"
          >
            <div v-if="data.shopInfo" class="shop-message-tjmd">
              <div class="center">
                <aside class="con">
                  <div class="itemLeft">
                    <!-- <p class="title">
                    {{ item.shopShortName }}
                  </p> -->
                    <div class="title">
                      <div>
                        {{ item.shopShortName }}
                      </div>
                      <dt>
                        <a :href="'tel:' + item.kefuPhone" class="telNumber">
                          <img
                            src="~@/assets/index_normal/jajsdj.png"
                            alt=""
                          /></a>
                      </dt>
                      <dt>
                        <a v-if="item.qrCode" href="javascript:;" class="telNumber" @click="handleQrcode(item)">
                          <img
                            src="https://img1.shop.10086.cn/fs/goods/fs_635642fae4b0fdd36b797c57.png"
                            alt=""
                          /></a>
                      </dt>

                    </div>

                    <div v-if="item.shopOpenStatus != 1" class="businesscontent">
                      <!-- 5营业中 6休息中 1不显示 2关厅 -->
                      <img
                        v-if="item.shopOpenStatus === 5"
                        src="~@/assets/index_img/shop_open.png"
                        alt=""
                      />
                      <img
                        v-if="item.shopOpenStatus === 6"
                        src="~@/assets/index_img/shop_close.png"
                        alt=""
                      />
                      <img
                        v-if="item.shopOpenStatus === 2"
                        src="~@/assets/index_img/shop_close_grey.png"
                        alt=""
                      />
                      <span>
                        {{
                          item.shopOpenStatus === 5
                            ? "营业中"
                            : item.shopOpenStatus === 6
                              ? "休息中"
                              : item.shopOpenStatus === 2
                                ? "关厅"
                                : ""
                        }}
                      </span>
                      <span v-if="item.shopOpenStatus === 6 && getCurrentHour(item.businessHours)!=='店铺休息'">营业时间</span>
                      <span
                        v-if="
                          item.shopOpenStatus === 5 || item.shopOpenStatus === 6
                        "
                      >
                        {{
                          getCurrentHour(
                            item.businessHours,
                            item.shopOpenStatus
                          )
                        }}
                      </span>
                    </div>

                    <div class="shop-address">
                      <div class="tesleft">
                        <p>
                          <img
                            src="@/assets/nearby/appnearby/shop_vip.png"
                            alt=""
                          /><span>{{ item.shopPropaganda }}</span>
                        </p>
                      </div>
                    </div>
                    <p class="address">
                      <img
                        src="@/assets/nearby/locationicon.png"
                        class="icon icon_adress"
                      />
                      <span v-if="item.distance" class="distance">
                        {{
                          item.distance > 1000
                            ? (item.distance / 1000).toFixed(2) + "km"
                            : item.distance.toFixed() + "m"
                        }}
                        {{
                          item.address && item.address.replace(/[\n|\s]+/g, "")
                            ? "|"
                            : ""
                        }}
                      </span>
                      <span :class="['addressName','paddingRigth']" :style="{'white-space':item.isShowExpansionButton?'nowrap':'inherit'}">{{ item.address }}</span>
                    </p>

                  </div>
                  <div class="itemRight">
                    <img
                      v-if="item.nearbyShopPicture"
                      :src="myGetImg(item.nearbyShopPicture)"
                    />
                    <img v-else src="~@/assets/nearby/default_img.png" alt="" />
                  </div>
                </aside>
              </div>

              <!-- 标签列表 -->
              <div v-if="item.tagList && item.tagList.length" :class="['tagList', !item.expansion ? 'heightYihang' : '']">
                <dl class="dl">
                  <div class="one">
                    <dt v-for="(tag,index) in item.tagList.slice(0,3)" :key="index">
                      {{ tag }}
                    </dt>
                  </div>
                  <div class="other">
                    <dt v-for="(tag,index) in item.tagList.slice(3)" :key="index">
                      {{ tag }}
                    </dt>
                  </div>
                </dl>

              </div>

              <!-- 展开收缩 -->
              <div v-if="item.isShowExpansionButton" class="zhankai">
                <span v-if="!item.expansion" @click="item.expansion = true">
                  <van-icon name="arrow-down" size="15" color="#333333" />
                  <i>展开</i>
                </span>
                <span v-if="item.expansion" @click="item.expansion = false">
                  <van-icon name="arrow-up" size="15" color="#333333" />
                  <i>收起</i>
                </span>
              </div>

              <div class="change-buten">
                <a
                  href="javascript:void(0);"
                  class="tesleft"
                  @click="goToShop(item.shopId)"
                >
                  <img
                    src="@/assets/nearby/appnearby/shop_ice.png"
                    alt=""
                  /><span>进店逛逛</span></a>
                <van-icon v-if="item.show == 1" name="minus" />
                <!-- <a :href="'tel:' + item.kefuPhone" class="tesright">
                  <img
                    src="@/assets/nearby/appnearby/ice_sj.png"
                    alt=""
                  /><span>电话联系</span></a> -->
                <a
                  v-if="item.show == 1"
                  href="javascript:void(0);"
                  class="tesright"
                  @click="quhao(item.shopId)"
                >
                  <img
                    src="https://img1.shop.10086.cn/fs/goods/fs_635f38c4e4b0fdd3b5a921a5.png"
                    alt=""
                  /><span>立即取号</span></a>
              </div>
            </div>
          </li>
        </ul>
      </van-list>
      <div v-if="isArrayEmpty(data.shopList) && isObjectEmpty(data.userLocation) && isObjectEmpty(data.userChooseLocation) && !data.loading && data.cur !== 1" class="needlocation">
        <img src="@/assets/nearby/appnearby/shop_wxx.png" />
        <p>您可以打开位置权限，查看附近的线上营业厅，或者在页面左上角选择查看指定省市的线上营业厅。</p>
      </div>
      <div v-else-if="isArrayEmpty(data.shopList) && isObjectEmpty(data.userLocation) && isObjectEmpty(data.userChooseLocation) && !data.loading && data.cur === 1" class="needlocation">
        <img src="@/assets/nearby/appnearby/shop_wxx.png" />
        <p>暂无店铺信息</p>
      </div>
      <div v-else-if="isArrayEmpty(data.shopList) && !data.loading && data.cur === 1" class="needlocation">
        <img src="@/assets/nearby/appnearby/shop_wxx.png" />
        <p>暂无店铺信息</p>
      </div>
      <div v-else-if="isArrayEmpty(data.shopList) && !isObjectEmpty(data.userChooseLocation) && !data.loading" class="needlocation">
        <img src="@/assets/nearby/appnearby/shop_wxx.png" />
        <p>当前地区尚未开通线上店，您可以换个地区试试，如上海，内蒙古等。</p>
      </div>
      <div v-else-if="isArrayEmpty(data.shopList) && !data.loading" class="needlocation">
        <img src="@/assets/nearby/appnearby/shop_wxx.png" />
        <p>暂未查到满足条件的店铺, 您可以换个关键词试试</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import Vue,{ reactive, ref, onMounted,watch,computed,nextTick } from "vue"
import "swiper/dist/css/swiper.css"
import Lodash from 'lodash'
import PriceCom from "@/components/index/price.vue"
import {
  Search,
  Swipe,
  SwipeItem,
  List,
  Icon,
  Toast,
  Loading,
  Button,
  Popup,
  Dialog
} from "vant"
import "vant/lib/index.css"
import VueAwesomeSwiper from "vue-awesome-swiper"
Vue.use(Search)
  .use(Swipe)
  .use(SwipeItem)
  .use(List)
  .use(Icon)
  .use(Toast)
  .use(Loading)
  .use(Button)
  .use(VueAwesomeSwiper)
  .use(Popup)
  .use(Dialog)

import UA from "@/utils/ua"
import geoApi from "@/api/geo"
import lbsApi from "@/api/lbs"
import loginUtils from "@/utils/login"
import shareUtilApi from "@/utils/share"
import provinceJson from "@/utils/province"
import {
  getImgUrl,
  getQueryString,
  getAllSearchParamsArray,
  setSearchParamsArray,
  isArrayEmpty,
  isObjectEmpty
} from "@/utils/utils"
import { getActData, getCustomerCode } from '@/api/shop'
import insertCode from "@/utils/insertCode"
import SwitchLocation from '@/components/lbs/switch-location/switch-location.vue'

const shareConfig = {
  title: "中国移动附近店铺",
  url: window.location.href,
  desc: "一键搜索帮您快速找店！",
  imgUrl: location.origin + require("@/assets/nearby/appnearby/wx_logo.jpg")
}
let popup = ref({
  show:false,
  wxCode:null,
  avatar:null,
})
let data = reactive({
  isArrayEmpty,
  isObjectEmpty,
  option: {
    pagination: {
      el: ".swiper-pagination"
    },
    autoHeight: true,
  },
  boutiquelist: null,
  shopInfo: null,
  loading: false,
  userLocation: {},
  shopList: [],
  tabTitle: ["离我最近", "我关注的店"],
  cur: 0,
  loadingList: false,
  finished: false,
  showNoYyData: false,
  pageNum: 1,
  tutalnum: 1,
  apppageNum: 1,
  appTutalnum:1,
  cmParam: {
    cm_code: ["fjdp_hdtj", "Fjdp_gntj"],
    province_id: 100,
    city_id: 100
  },
  keywords: "",
  navShopList: false,
  shopListNav: null,
  isLogined: null,
  goodsItemShop: null,
  provindeName: null,
  user: null,
  pageInfoshopId: null,
  /* 输入关键字搜索后改为true, 初始化进入页面为false */
  isSearchBoolean:false,
  from :null,
  userChooseLocation:{},
  isInit:false
})
watch(()=>data.pageInfoshopId, (value) => {
  if (value) {
    shopQueryOnLine()
  }
},{
  immediate: true
})
const filterGoodsLink = computed(()=>(goodsLink)=>{
  if (goodsLink && goodsLink != "") {
    let allParams = getAllSearchParamsArray(location.href)
    let ac_id = allParams["WT.ac_id"]
    let WTACID = ac_id ? ac_id : "211210_YD_PD"
    return setSearchParamsArray(goodsLink, {
      "WT.ac_id": WTACID
    })
  }
  return "javascript:void(0);"
})
onMounted(async()=>{
  data.from = getQueryString("from")
  //微信分享配置
  if (UA.isApp) {
    await shareUtilApi.appShare(shareConfig)
  } else {
    shareUtilApi.changeWxShareConfig(shareConfig)
  }
  loginUtils.login(false, true, logined)
})
function switchConfirm(parame){
  data.pageNum = 1
  data.shopList = []
  if(parame){
    data.userChooseLocation = parame
  }

  if (data.cur === 0) {

    if (data.pageNum === 1 && !data.isInit && !data.isReload) {
      data.isReload = true
      getAllShop()
      return
    }else{
      data.isInit = true
      getNearShop()
    }
    // data.getNearShopNav()
  } else if (data.cur === 1) {
    data.isInit = true
    getVipShop()
  }
}
function homeData(item){
  return new Promise(resolve => {
    getActData({
      shopId:item.shopId
    }).then(res => {
      resolve(res)
    }).catch(() => {
      resolve({})
    })
  })
}
async function handleQrcode(item){
  let sharingCode = item.sharingCode

  Toast.loading({
    message: '加载中...',
    forbidClick: true,
    duration:0
  })
  if(!sharingCode){
    let homedata = await homeData(item)
    let actData = Lodash.get(homedata,'data.actData',[])
    let sharing = actData.find(item => item.componentCode == "sharing")
    let contact = actData.find(item => item.componentCode == "contact")
    let floor = null

    if(contact && Lodash.get(contact,'dataList.floor', null)){
      floor = Lodash.get(contact,'dataList.floor', null)
    }

    if(sharing && Lodash.get(sharing,'dataList.floor', null)){
      floor = Lodash.get(sharing,'dataList.floor', null)
    }

    sharingCode = Lodash.get(floor,'sharingCode', null)

    if(!sharingCode){
      Toast('店长企微暂未开通，您可以换个方式联系')
      return
    }
  }

  getCustomerCode({sharingCode}).then(res => {
    Toast.clear()

    if(res.code){
      Toast('店长企微暂未开通，您可以换个方式联系')
      return
    }

    const qrCode = res.data.contactQrcode || res.data.qrCode

    if(!qrCode){
      Toast('店长企微暂未开通，您可以换个方式联系')
      return
    }

    popup.value.avatar = res.data.avatar
    popup.value.wxCode = qrCode
    popup.value.show = true
  })
}
function init() {
  getUserLocation(data.cur)
}
function logined(res) {
  data.user = res
  data.isLogined = res && res.UserName > ""
  let province
  if (res) {
    province = provinceJson.data[res.userProvince + "_" + res.userCity]
  }
  if (province !== undefined) {
    let provinceCity = province.split("_")
    data.provindeName = provinceCity[0]
  }
  init()
}
function switchTab(index) {
  data.cur = index
  data.pageNum = 1
  data.apppageNum = 1
  if (index === 0) {
    data.isInit = false

    // 没开启位置，没选择位置, 不会出现数据啊
    if(isObjectEmpty(data.userLocation) && isObjectEmpty(data.userChooseLocation)){
      return
    }

    getNearShop()
  } else if (index === 1) {
    data.isInit = false
    getVipShop()
  }
}
function initBoutique(boutiqueRes, shopInfo) {
  if (
    boutiqueRes.code === 0 &&
    boutiqueRes.data &&
    boutiqueRes.data.shops &&
    boutiqueRes.data.shops.length
  ) {
    let shops = boutiqueRes.data.shops
    if (shops.length > 6) {
      shops.length = 6
    }
    data.boutiquelist = shops
    if (boutiqueRes.data.shops[0].distance <= 3000) {
      initTopProvince(boutiqueRes.data.shops[0])
      data.boutiquelist.splice(0, 1)
    } else {
      initTopProvince(shopInfo)
    }
  } else {
    data.boutiquelist = null
    initTopProvince(shopInfo)
  }
}
function initTopProvince(shopInfo) {
  if(!shopInfo){
    return
  }
  data.shopInfo = shopInfo
  data.pageInfoshopId  = shopInfo.shopId
  let province
  if (data.user === null) {
    province = provinceJson.data[shopInfo.province + "_" + shopInfo.city]
  }
  if (province !== undefined) {
    let provinceCity = province.split("_")
    data.provindeName = provinceCity[0]
  }
}

function getCurrentHour(businessHours, shopOpenStatus) {
  let week = new Date().getDay(),
    str = ""
  if(businessHours){
    if(week>0){
      str = businessHours[week-1]
    }else{
      str = businessHours[6]
    }
  }else{
    str = ""
  }
  return str
}
function returnStringLength(message){
  let lens = 0
  if(message === null || message === undefined || message.length==0) {
    return lens
  }
  for (let i = 0; i < message.length; i++) {
    if ((message.charCodeAt(i) >= 0) && (message.charCodeAt(i) <= 255))
    { lens = lens + 1}
    else{ lens = lens + 2 }
  }
  return lens
}
/* 公共处理 展开收起 相关js */
function filterDataExpansion(data = {}){
  let item = JSON.parse(JSON.stringify(data))

  // 拿到标签length
  let strLength = 0
  strLength = item.tagList.length
  item.tagLength = strLength

  // 拿到地址length
  let addressLength = returnStringLength(item.address)
  item.addressLength = addressLength

  // 拿到推荐语length
  let shopPropagandaLength = returnStringLength(item.shopPropaganda)
  item.shopPropagandaLength = shopPropagandaLength

  // 决定展开收起的变量
  if(strLength > 3 || addressLength > 29 || shopPropagandaLength > 26){
    item.expansion = false

    // 代表当前是否需要显示展开按钮
    item.isShowExpansionButton = true
  }else{
    item.expansion = true

    // 代表当前是否需要显示展开按钮
    item.isShowExpansionButton = false
  }

  return item
}
//附近店铺
function getNearShop(isfirst) {
  const {provinceId,cityId,regionCode} = data.userChooseLocation
  // 只显示搜索列表, 其他的都不显示
  if(data.isInit){
    data.shopInfo = {}
    data.goodsItemShop = null
    data.isSearchBoolean = true
    data.boutiquelist = []
  }
  const { latitude, longitude } = data.userLocation || {}

  const options = {
    keywords: data.keywords,
    pageNum: data.pageNum,
    pageSize: 10,
    province:provinceId,
    city:cityId,
    county:regionCode
  }

  // 获取用户所在省id
  let userLocationToStore = sessionStorage.getItem('userLocationToStore')
  if(userLocationToStore){
    userLocationToStore = JSON.parse(userLocationToStore)
  }else{
    userLocationToStore = {}
  }

  // 用户所在省与选择省相同
  if(provinceId == userLocationToStore.provinceId){
    options.latitude = latitude
    options.longitude = longitude
  }
  if(options.pageNum==1){
    data.loading = true
    data.loadingList = true
  }
  lbsApi
    .getNearShop(options)
    .then(res => {
      // 附近店铺排查问题用
      // console.log(options)
      // console.log(res)
      /* 增加是否标签收起字段 */
      res.data.shops = res.data.shops.map(item => {
        item = filterDataExpansion(item)
        return item
      })
      data.loading = false
      data.loadingList = false
      if (res.code === 0 && res.data && res.data.shops) {
        data.tutalnum = Math.ceil(res.data.total / res.data.pageSize)

        if (data.pageNum === 1) {
          data.shopList = res.data.shops
        } else {
          data.shopList = [...data.shopList, ...res.data.shops]
        }

        if (data.pageNum < data.tutalnum) {
          data.pageNum = data.pageNum + 1
          data.finished = false
          data.showNoYyData = false
        } else {
          data.finished = true
          data.showNoYyData = true
        }
      } else {
        data.shopList = []
      }
    })
}
//附近店铺&精品店铺
function getAllShop() {
  const { latitude, longitude } = data.userLocation || {}
  const {provinceId,cityId,regionCode} = data.userChooseLocation
  data.loading = true
  data.loadingList = true
  Promise.all([
    lbsApi.getNearShop({
      latitude,
      longitude,
      keywords: data.keywords,
      pageNum: data.pageNum,
      pageSize: 10,
      province:provinceId,
      city:cityId,
      county:regionCode
    }),
    lbsApi.getNearShop({
      latitude,
      longitude,
      boutique: 2,
      keywords: data.keywords,
      pageNum: data.pageNum,
      pageSize: 50,
      distance: 50,
      province:provinceId,
      city:cityId,
      county:regionCode
    })
  ]).then(([nearShopRes, boutiqueRes]) => {
    if (
      nearShopRes.code === 0 &&
      nearShopRes.data &&
      nearShopRes.data.shops
    ) {
      /* 增加是否标签收起字段 */
      nearShopRes.data.shops = nearShopRes.data.shops.map(item => {
        item = filterDataExpansion(item)
        return item
      })

      boutiqueRes.data.shops = boutiqueRes.data.shops.map(item => {
        item = filterDataExpansion(item)
        return item
      })

      data.tutalnum = Math.ceil(nearShopRes.data.total / 10)
      data.shopList = nearShopRes.data.shops
      initBoutique(boutiqueRes, nearShopRes.data.shops[0])
      if (data.pageNum < data.tutalnum) {
        data.pageNum = data.pageNum + 1
        data.finished = false
        data.showNoYyData = false
      } else {
        data.finished = true
        data.showNoYyData = true
      }
    } else {
      data.shopList = []
    }
    data.isInit = true

    nextTick(() => {
      data.loading = false
      data.loadingList = false
    })
  })
}
//附近店铺搜索
function getNearShopNav() {
  const {provinceId,cityId,regionCode} = data.userChooseLocation
  const { latitude, longitude } = data.userLocation || {}
  lbsApi
    .getNearShop({
      latitude,
      longitude,
      keywords: data.keywords,
      pageNum: 1,
      pageSize: 10,
      province:provinceId,
      city:cityId,
      county:regionCode
    })
    .then(res => {
      if (res.code === 0 && res.data && res.data.shops) {
        /* 增加是否标签收起字段 */
        res.data.shops = res.data.shops.map(item => {
          item = filterDataExpansion(item)
          return item
        })

        data.shopList = res.data.shops

        // 只显示搜索列表, 其他的都不显示
        data.shopInfo = {}
        data.goodsItemShop = null
        data.isSearchBoolean  = true
        data.boutiquelist = []

        // data.shopListNav = res.data.shops
        // data.navShopList = true
      } else {
        data.navShopList = false
        Toast("未获取到当前搜索内容，请更换搜索内容")
      }
    })
}
//附近店铺会员店
function getVipShop() {
  const {provinceId,cityId,regionCode} = data.userChooseLocation
  const { latitude, longitude } = data.userLocation || {}
  data.loading = true
  data.loadingList = true
  if(data.isInit){
    data.shopInfo = {}
    data.goodsItemShop = null
    data.isSearchBoolean = true
    data.boutiquelist = []
  }
  lbsApi
    .getVipShop({
      latitude,
      longitude,
      keywords: "",
      pageNum: data.apppageNum,
      pageSize: 10,
      province:provinceId,
      city:cityId,
      county:regionCode
    })
    .then(res => {
      if (res.code === 0 && res.data && res.data.shops) {
        /* 增加是否标签收起字段 */
        res.data.shops = res.data.shops.map(item => {
          item = filterDataExpansion(item)
          return item
        })

        data.appTutalnum = Math.ceil(res.data.total / 10)
        if (data.apppageNum === 1) {
          data.shopList = res.data.shops
        } else {
          data.shopList = [...data.shopList, ...res.data.shops]
        }
        if (data.apppageNum < data.appTutalnum) {
          data.apppageNum = data.apppageNum + 1
          data.finished = false
          data.showNoYyData = false
        } else {
          data.finished = true
          data.showNoYyData = true
        }
      } else {
        data.shopList = []
      }
      nextTick(() => {
        data.loading = false
        data.loadingList = false
      })
    })
}
function quhao(shopID) {
  if (data.isLogined) {
    lbsApi
      .getUrlReq({
        lat: "",
        lon: "",
        pageType: 2,
        shopId: shopID
      })
      .then(res => {
        if (res.code === 0) {
          location.href = res.data.url
        } else {
          Toast(res.message)
        }
      })
  } else {
    loginUtils.login(true, true, logined)
  }
}
function goToShop(shopId) {
  let allParams = getAllSearchParamsArray(location.href)
  let ac_id = allParams["WT.ac_id"] || Vue.$cookies.get("ac_id")
  let WTACID = ac_id ? ac_id : "211210_YD_PD"
  if (data.from === "home") {
    location.href = "/yundian/my/index.html?shopId=" + shopId
  } else {
    location.href = `/yundian/index.html?shopId=${shopId}&WT.ac_id=${WTACID}`
  }
}
//查询热点商品
function shopQueryOnLine() {
  const goodsdhop = []
  lbsApi
    .shopGoodsQuer({
      adpCode: "hotGoods",
      status: 2,
      shopId: data.pageInfoshopId
    })
    .then(res => {
      if (res.code === 0) {
        let dale = res.data
        if (dale !== null && dale.length >= 1) {
          dale.forEach((value, item) => {
            if (value.picture && value.picture !== "") {
              if (value.goodsSource && value.goodsSource === 2) {
                value.imgUrl = value.picture
              } else {
                value.imgUrl = getImgUrl(value.picture)
              }
            }
            if (
              (value.goodsStatus === 51 &&
                value.o2oGoodsStatus === 3) ||
              (value !== null && value.goodsSource === 2)
            ) {
              goodsdhop.push(value)
            }
          })
          // if (
          //   (dale[0] !== null &&
          //     dale[0].goodsStatus === 51 &&
          //     dale[0].o2oGoodsStatus === 3) ||
          //   (dale[0] !== null && dale[0].goodsSource === 2)
          // ) {
          //   goodsdhop.push(dale[0])
          // }
          // if (
          //   (dale[1] && dale[1] !== null &&
          //     dale[1].goodsStatus === 51 &&
          //     dale[1].o2oGoodsStatus === 3) ||
          //   (dale[1] !== null && dale[1].goodsSource === 2)
          // ) {
          //   goodsdhop.push(dale[1])
          // }
          // if (
          //   (dale[2] &&
          //     dale[2] !== null &&
          //     dale[2].goodsStatus === 51 &&
          //     dale[2].o2oGoodsStatus === 3) ||
          //   (dale[2] && dale[2] !== null && dale[2].goodsSource === 2)
          // ) {
          //   goodsdhop.push(dale[2])
          // }
          if (goodsdhop.length > 0) {
            data.goodsItemShop = goodsdhop
          }
        }
      }
    })
}
async function getUserLocation(index) {
  data.loading = true
  data.pageNum = 1
  data.apppageNum = 1
  geoApi.getPosition().then(res => {
    // res = {//调试页面专用，解除注释即可看到页面
    //   code:0,
    //   data:{
    //     locProvince:"北京",
    //     latitude: 39.822288,
    //     longitude: 116.294807
    //   }
    // }
    if (res.code) {
      Dialog.alert({
        title: '温馨提示',
        confirmButtonColor: "#06acea",
        confirmButtonText:'查看指定区域店铺',
        message: '您可以打开位置权限，查看附近的线上营业厅，或者在页面左上角选择查看指定省市的线上营业厅。',
      })

      setTimeout(() => {
        Dialog.close()
      }, 5000)
      data.userLocation = null
      data.isInit = true
    } else {
      // const { locProvince } = res.data
      // data.provindeName = locProvince
      if (res.data && res.data.latitude) {
        data.userLocation = {
          latitude:res.data.latitude,
          longitude:res.data.longitude
        } || {}
      } else {
        Dialog.alert({
          title: '温馨提示',
          confirmButtonColor: "#06acea",
          confirmButtonText:'查看指定区域店铺',
          message: '您可以打开位置权限，查看附近的线上营业厅，或者在页面左上角选择查看指定省市的线上营业厅。',
        })

        setTimeout(() => {
          Dialog.close()
        }, 5000)
        data.userLocation = null
        data.isInit = true
      }
    }
    nextTick(() => {
      data.loading = false
    })
  })
  geoApi.getLocation().then(res => {
    const { province } = res.data
    data.cmParam.province_id = province || 100
    data.cmParam.city_id = province || 100 //使用省会
  })
}
function myGetImg(src) {
  return getImgUrl(src)
}
</script>
<style>
body {
  background: #fff !important;
  font-size: 12px;
}
.icpinfo {
  display: none;
}
.nav .van-field__control,
.nav .van-field__control,
.nav .van-cell__value--alone {
  color: #fff;
}
.nav .van-icon-search::before {
  /* content: "\F07D"; */
  content: "\E710";
  color: #fff;
}
.van-list__finished-text {
  width: 225px;
  text-align: center;
  margin: 3.75px auto;
  height: 49.875px;
  line-height: 22.5px;
}
.swiper-pagination-bullet {
  width: 12px;
  height: 4px;
  background: #e2e2e2;
  border-radius: 2px;
  box-shadow: 0px -1px 3px 0px rgba(0, 0, 0, 0.11) inset;
}
.swiper-pagination-bullet-active {
  background: linear-gradient(180deg, #89f7fe 0%, #66a6ff 100%);
  box-shadow: 0px -1px 3px 0px rgba(4, 127, 220, 0.56) inset;
}
</style>
<style lang="scss" scoped>

.appnearBy{
  :deep(.numColor){
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.loading-area {
  width: 40%;
  text-align: center;
  margin: 40px auto;
}
.needlocation,
.loading-area {
  width: 94%;
  margin: 0 auto;
  padding: 172.5px 19.875px;
  font-size: 12.750000000000002px;
  line-height: 180%;
  background: #ffffff;
  border-radius: 7.5px;
  -webkit-box-shadow: 0px 2px 15px 0px rgba(241, 243, 250, 0.9);
  box-shadow: 0px 2px 15px 0px rgba(241, 243, 250, 0.9);
  text-align: center;
  color: #999;
  img {
    width: 60px;
    display: block;
    margin: 10px auto;
  }
}
.index {
  color: #333333;
  a {
    color: #333333;
  }
  .nav {
    &.searchActive{
      background:none;
      &::before{
        position: absolute;
        height:53px;
        left:0;
        top:0;
        width:100%;
        content:'';
        background: linear-gradient(23deg, #5fc8ff -33%, #8372fe 99%);
      }
    }
    height: 150px;
    background: linear-gradient(23deg, #5fc8ff -33%, #8372fe 99%);
    //-webkit-box-shadow: 0px 2px 10.875px 0px rgba(109,177,255,0.29);
    //box-shadow: 0px 2px 10.875px 0px rgba(109,177,255,0.29);
    padding: 10px 0;
    position: relative;
    display: flex;
    .telphone-name {
      text-align: center;
      color: #fff;
      font-size: 14.25px;
      height:30px;
      display: flex;
      align-items: center;
      margin-left: 10px;
      margin-right: 10px;
    }
    .van-search {
      flex: 1;
      height: 30px;
      margin-right: 15px;
      border: none;
      background: #3d1bad;
      border-radius: 21px;
      color: #fff;
      z-index: 2;
      .van-search__content {
        background: none;
        padding-left: 0;
      }
    }
    .nav-search-tkzh {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.6);
      z-index: 1;
    }
    .nav-search-con {
      position: absolute;
      top: 41.25px;
      right: 18.75px;
      width: 285px;
      background: #fff;
      line-height: 22.5px;
      padding: 7.5px 11.25px;
      font-size: 12.750000000000002px;
      z-index: 1;
      height: 210px;
      overflow: scroll;
      border-radius: 7.5px;
      -webkit-box-shadow: 0px 2px 15px 0px rgba(241, 243, 250, 0.9);
      box-shadow: 0px 2px 15px 0px rgba(241, 243, 250, 0.9);
      li {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .shop-message-top {
    margin-top: -97.5px;
    position: relative;
  }
  .shop-message {
    width: 94%;
    height: auto;
    position: relative;
    margin: 0 auto;
    padding: 18.75px 0 3.75px;
    background: #ffffff;
    border-radius: 7.5px;
    -webkit-box-shadow: 0px 2px 15px 0px rgba(241, 243, 250, 0.9);
    box-shadow: 0px 2px 15px 0px rgba(241, 243, 250, 0.9);
    overflow: hidden;
    .message-sel {
      position: absolute;
      top: 0;
      left: 0;
      width: 45px;
      height: auto;
    }
    .line {
      clear: both;
      border-top: 1px dotted rgba(241, 243, 250, 1);
      position: relative;
      &::before {
        display: inline-block;
        content: "●";
        position: absolute;
        top: -9.375px;
        left: -9px;
        color: rgba(241, 243, 250, 1);
        font-size: 18.75px;
      }
      &::after {
        display: inline-block;
        content: "●";
        position: absolute;
        top: -9.375px;
        right: -9px;
        color: rgba(241, 243, 250, 1);
        font-size: 18.75px;
      }
    }
    .shop-message-bdrx {
      h3 {
        padding: 17.25px 15px;
        font-size: 15.75px;
        font-weight: 600;
      }
      .hot-product-card {
        clear: both;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        width: 100%;
        overflow: hidden;
        padding: 0 3.75px 11.25px;
        .goods-item {
          flex: 1;
          width: 33.3%;
          overflow: hidden;
          padding: 0 3.75px;

          .image {
            display: block;
            margin: 0 auto 9px;
            width: 75px;
            height: 75px;
          }
          p {
            font-size: 12.750000000000002px;
            height: 21.000000000000004px;
            line-height: 21.000000000000004px;
          }
          .title1 {
            color: #333;
          }
          .title2 {
            color: rgb(153, 153, 153);
            font-size: 12px;
          }
          .num-color,.numColor {
            font-weight: 700;
            color: #ed2668;
          }
          .over-ell {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
  .shop-message-tjmd {
    padding: 0 18.75px;
    .center {
      display: flex;
      flex-direction: column;
      clear: both;
      .shop-name {
        clear: both;
        line-height: 30px;
        display: flex;
        margin-bottom: 3.75px;
        .tesleft {
          text-align: left;
          flex: 5;
          font-size: 16px;
          font-weight: 600;
          color: #333333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .tesright {
          text-align: right;
          flex: 1;
          font-size: 13.5px;
          color: #cccccc;
        }

      }
      .shop-address {
        clear: both;
        display: flex;
        align-items: center; /*定义body的元素垂直居中*/
        //justify-content: center; /*定义body的里的元素水平居中*/
        font-size: 12px;
        line-height: 22.5px;
        margin-top: 13px;
        height: 14px;
        .tesleft {
          text-align: left;
          width: 90%;
          float: left;
          p {
            line-height: 22.5px;
            color: #999999;
            clear: both;
            white-space: normal;
            word-break: break-all;
            word-wrap: break-word;
            img {
              display: block;
              width: 17px;
              height: auto;
              float: left;
              transform: translate(-2px, 7px);
            }
            span {
              display: inline-block;
              width: 90%;
              color: #333333;
              padding-top: 5.250000000000001px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
        .tesright {
          text-align: right;
          width: 9%;
          float: right;
          img {
            display: inline-block;
            width: 17.25px;
            height: auto;
          }
        }
      }
      .title{
        display: flex;
        align-items: center;
        padding-right:11.25px;
        &>div{
          &:nth-of-type(1){
            overflow:hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      .telNumber{
        width: 20px;
        height: 20px;
        display: block;
        margin-left:6px;
        img{
          width:100%;
          height:100%;
        }
      }
    }
    .change {
      clear: both;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 15px 0 22.5px;
      text-align: center;
      a {
        display: inline-block;
        height: 37.5px;
        // line-height: 37.5px;
        border-radius: 18.75px;
        color: #ffffff;
        font-size: 13.5px;
        width: 48%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .tesleft {
        background: linear-gradient(90deg, #2ddbfa, #5083f5);
        -webkit-box-shadow: 0px 2.25px 3.75px 0px rgba(189, 216, 253, 0.9);
        box-shadow: 0px 2.25px 3.75px 0px rgba(189, 216, 253, 0.9);
        margin-right: 22.5px;
      }
      .tesright {
        background: linear-gradient(90deg, #fb77b5, #f64040);
        -webkit-box-shadow: 0px 2.25px 3.75px 0px rgba(252, 190, 190, 0.9);
        box-shadow: 0px 2.25px 3.75px 0px rgba(252, 190, 190, 0.9);
      }
    }
  }
  .cm-icon {
    width: 80%;
    margin: 15px auto 11.25px;
    .item {
      font-size: 12.750000000000002px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      font-weight: 600;
      li {
        flex: 1;
        color: #a26e36;
      }
      .active {
        font-size: 15px;
        color: #1a9bfc;
      }
      img {
        width: 22.5px;
        height: auto;
        margin: 3.75px auto;
        display: block;
      }
    }
  }
  .boutique {
    margin-top: 18px;
    padding-bottom: 20px;
    .boutique-list {
      .boutique-title {
        img {
          width: 99px;
          height: 24px;
        }
      }
      width: 94%;
      height: auto;
      margin: 0 auto 13.5px;
      padding-top: 11.25px;
      background: #ffffff;
      border-radius: 7.5px;
      border: 1px solid #f0f0f0;
      -webkit-box-shadow: 0px 2px 15px 0px rgba(241, 243, 250, 0.9);
      box-shadow: 0px 2px 15px 0px rgba(241, 243, 250, 0.9);
      &.noimg {
        display: none;
      }
      .shop-message-tjmd {
        padding: 0;
        overflow: hidden;
        .center {
          padding: 0 15px;
          height: 90px;
        }
        .shop-address {
          .tesleft {
            width: 100%;
            p {
              font-size: 12.750000000000002px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
        .change-buten {
          clear: both;
          display: flex;
          justify-content: center;
          align-items: center;
          border-top: 1px solid #f0f0f0;
          margin-top: 11.25px;
          text-align: center;
          a {
            // display: inline-block;
            height: 45px;
            // line-height: 45px;
            color: #ffffff;
            font-size: 15px;
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 22.5px;
              height: auto;
              margin-right: 7.5px;
            }
          }
          .van-icon-minus {
            color: #f0f0f0;
            transform: rotate(90deg);
            -ms-transform: rotate(90deg);
            -moz-transform: rotate(90deg);
            -webkit-transform: rotate(90deg);
            -o-transform: rotate(90deg);
          }
          .tesleft {
            color: #1a9bfc;
          }
          .tesright {
            color: #f7454b;
          }
        }
      }
    }
    .swiper-container {
      padding-bottom: 20px;
    }
  }

  .shop-list {
    li {
      width: 94%;
      height: auto;
      margin: 0 auto 13.5px;
      padding-top: 11.25px;
      background: #ffffff;
      border-radius: 7.5px;
      border: 1px solid #f0f0f0;
      -webkit-box-shadow: 0px 2px 15px 0px rgba(241, 243, 250, 0.9);
      box-shadow: 0px 2px 15px 0px rgba(241, 243, 250, 0.9);
      &.noimg {
        display: none;
      }
      .shop-message-tjmd {
        padding: 0;
        overflow: hidden;
        .center {
          padding: 0 15px;
        }
        .shop-address {
          .tesleft {
            width: 100%;
            p {
              font-size: 12.750000000000002px;
            }
          }
        }
        .change-buten {
          clear: both;
          display: flex;
          justify-content: center;
          align-items: center;
          border-top: 1px solid #f0f0f0;
          margin-top: 11.25px;
          text-align: center;
          a {
            display: inline-block;
            height: 45px;
            line-height: 45px;
            color: #ffffff;
            font-size: 15px;
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 22.5px;
              height: auto;
              margin-right: 7.5px;
            }
          }
          .van-icon-minus {
            color: #f0f0f0;
            transform: rotate(90deg);
            -ms-transform: rotate(90deg);
            -moz-transform: rotate(90deg);
            -webkit-transform: rotate(90deg);
            -o-transform: rotate(90deg);
          }
          .tesleft {
            color: #1a9bfc;
          }
          .tesright {
            color: #f7454b;
          }
        }
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.wechpopContent{
  width:280px;
    border-radius:12px;
    overflow: hidden;
    background: #fff;
    .bgimg{
      img{
        width:100%;
      }
    }
    .text{
      .dzimg{
        display: flex;
        justify-content: center;
        align-items: center;
        img{
          width:140px;
          height:140px;
        }
    position: relative;
    img.positiondzimg{
      position: absolute;
      width:30px;
      height:30px;
      left:0;
      right:0;
      top:0;
      bottom:0;
      margin:auto;
    }
        margin:7.5px 0;
      }

      .left{
        padding:0px 14.5px;
        margin:7.5px 0;
        padding-bottom: 7.5px;
        margin-top: 15px;
        .tit{
          font-size: 15px;
          color:#000;
          margin-bottom: 5px;
          margin-top: 2.5px;
        }
        .com{
          color:#000;
          font-size: 10px;
          p{
            padding:2.5px 0;
            line-height: 1.8;
          }
        }
      }
    }
  }

  .zhankai{
      display: flex;
  padding:0 15px;
    justify-content: flex-end;
      align-items: center;
      span{
        font-size: 10px;
        color:#333;
        display: flex;
          align-items: center;
          i{
            font-style:normal;
            margin-left: 3px;
          }
      }
    }
.tagList{
  padding:0 15px;
  display: flex;
  &.heightYihang{
      height:34px;
      overflow: hidden;
    }

  dl{
    display: flex;
    flex-flow: wrap;
    margin-top: 10px;
    flex:1;
    min-width:0;
    .other{
      display: flex;
      flex-flow:wrap;
      width:100%;
      }
      .one{
        display: flex;
        width:100%;
      }
    dt{
      font-size: 10px;
      background: #dff1fb;
      border:2px solid #51a7ea;
      border-radius:10px;
      padding:3px 4px;
      color:#51a7ea;
      margin-right: 5px;
      margin-bottom: 7px;
    }
  }
  &.paddingleft0{
    padding-left: 0;
    padding-right: 0;
  }
}

/* 新增样式 */
.index{

.shop-message-tjmd,.nearbyitem{

  .zhankai{
    margin-top: 5px;
  }

.con{
  display: flex;
  .itemLeft{
    flex:1;
    min-width:0;

    .shop-address{
      height:auto;
      line-height: normal;
    }

    .tesleft{
      p{
        line-height: normal;
        display: flex;
        align-items: flex-start;
        // align-items: center;
        img{
          transform: none;
        }
        span{
          line-height: 1.5;
          padding-top: 0;
          word-break: keep-all;
        }
      }
    }
  }
  .itemRight{
    position: static;
    margin-right: 0;
  }
}

.businesscontent{
  padding-top: 5px;
  width:100%;
  height:auto;
}
.shop-address{
    .tesleft{
      p{
        span{
          flex:1;
          min-width:0;
          padding-right: 15px;
        }
      }
    }
  }

&.heightYihang{
  .shop-address{
    .tesleft{
      p{
        align-items: flex-start;
        span{
          overflow: inherit !important;
          text-overflow: inherit !important;
          white-space: normal !important;
          padding-right: 8px;
        }
      }
    }
  }
  .addressName{
    overflow: inherit;
    text-overflow: inherit;
    white-space: inherit !important;
    word-break: break-all;
  }
  .address{
    align-items: flex-start;
  }
}
.address{
  display: flex;
  height:auto;
  width:100% !important;
  padding:5px 0 !important;
  overflow: inherit;
  text-overflow: inherit;
  white-space: inherit;
  line-height: 1.5;
  align-items: flex-start;
  span{
    line-height: inherit;
    padding-top: 0px;
  }
}
.icon_adress{
  transform: none;
}
.addressName{
  flex:1;
  min-width:0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-left: 3px;
  display: block !important;
  &.paddingRigth{
    padding-right: 15px;
  }
}
}
}

.index .boutique .boutique-list .shop-message-tjmd .center{
  height:auto;
}

.index{
  .nav{
    padding-left: 10px;
    :deep(.van-icon-search::before){
          content: '\E710';
    }
    .switch-location{
      padding-top: 8px;
    }
  }
}

</style>
